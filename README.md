# MyTechRead - Full-Stack Blogging Website

A modern, responsive blogging platform built with React.js, Tailwind CSS, Clerk authentication, and Supabase backend.

## Features

### Main Website
- 🎨 Modern, responsive design with Tailwind CSS
- 🌓 Light/Dark mode toggle
- 📱 Mobile-first responsive design
- 🔍 Search and filter functionality
- 📖 Individual blog post pages
- ⚡ Fast routing with React Router

### Admin Dashboard
- 🔐 Secure authentication with Clerk
- ✍️ Rich text editor for creating blogs
- 📊 Analytics dashboard with charts
- 🖼️ Image upload functionality
- 📈 Traffic statistics and performance metrics
- 🎯 Professional admin interface

### Backend
- 🗄️ Supabase database for blog storage
- 🔒 Row Level Security (RLS) policies
- 📸 Image storage with Supabase Storage
- 📊 Analytics tracking system

## Tech Stack

- **Frontend**: React.js 19, Vite
- **Styling**: Tailwind CSS
- **Authentication**: Clerk
- **Database**: Supabase (PostgreSQL)
- **Routing**: React Router DOM
- **Icons**: Lucide React
- **Charts**: Recharts
- **Rich Text Editor**: React Quill

## Quick Start

1. **Install dependencies:**
   ```bash
   npm install
   ```

2. **Set up environment variables:**
   - Copy `.env.example` to `.env`
   - Add your Clerk and Supabase credentials

3. **Set up database:**
   - Create a Supabase project
   - Run the SQL from `database-schema.sql`
   - Create a `blog-images` storage bucket

4. **Run development server:**
   ```bash
   npm run dev
   ```

5. **Access admin panel:**
   - Visit `/admin` and sign in with Clerk

## Environment Variables

```env
VITE_CLERK_PUBLISHABLE_KEY=your_clerk_key
VITE_SUPABASE_URL=your_supabase_url
VITE_SUPABASE_ANON_KEY=your_supabase_key
VITE_SITE_NAME=MyTechRead
VITE_SITE_URL=https://mytechread.in
```

## Project Structure

```
src/
├── components/          # Reusable components
├── pages/              # Page components
├── contexts/           # React contexts
├── lib/               # Utilities and services
└── App.jsx            # Main app with routing
```

## License

MIT License
