import { createClient } from '@supabase/supabase-js'

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY

export const supabase = createClient(supabaseUrl, supabaseAnonKey)

// Blog related functions
export const blogService = {
  // Get all published blogs
  async getBlogs() {
    const { data, error } = await supabase
      .from('blogs')
      .select('*')
      .eq('published', true)
      .order('created_at', { ascending: false })
    
    if (error) throw error
    return data
  },

  // Get a single blog by slug
  async getBlogBySlug(slug) {
    const { data, error } = await supabase
      .from('blogs')
      .select('*')
      .eq('slug', slug)
      .eq('published', true)
      .single()
    
    if (error) throw error
    return data
  },

  // Create a new blog (admin only)
  async createBlog(blogData) {
    const { data, error } = await supabase
      .from('blogs')
      .insert([blogData])
      .select()
    
    if (error) throw error
    return data[0]
  },

  // Update a blog (admin only)
  async updateBlog(id, blogData) {
    const { data, error } = await supabase
      .from('blogs')
      .update(blogData)
      .eq('id', id)
      .select()
    
    if (error) throw error
    return data[0]
  },

  // Delete a blog (admin only)
  async deleteBlog(id) {
    const { error } = await supabase
      .from('blogs')
      .delete()
      .eq('id', id)
    
    if (error) throw error
  },

  // Upload image
  async uploadImage(file, bucket = 'blog-images') {
    const fileExt = file.name.split('.').pop()
    const fileName = `${Math.random()}.${fileExt}`
    const filePath = `${fileName}`

    const { error: uploadError } = await supabase.storage
      .from(bucket)
      .upload(filePath, file)

    if (uploadError) throw uploadError

    const { data } = supabase.storage
      .from(bucket)
      .getPublicUrl(filePath)

    return data.publicUrl
  }
}

// Analytics functions
export const analyticsService = {
  // Track page view
  async trackPageView(page, blogId = null) {
    const { error } = await supabase
      .from('analytics')
      .insert([{
        page,
        blog_id: blogId,
        timestamp: new Date().toISOString(),
        user_agent: navigator.userAgent
      }])
    
    if (error) console.error('Analytics tracking error:', error)
  },

  // Get analytics data
  async getAnalytics(startDate, endDate) {
    const { data, error } = await supabase
      .from('analytics')
      .select('*')
      .gte('timestamp', startDate)
      .lte('timestamp', endDate)
      .order('timestamp', { ascending: false })
    
    if (error) throw error
    return data
  },

  // Get blog performance
  async getBlogPerformance() {
    const { data, error } = await supabase
      .from('analytics')
      .select(`
        blog_id,
        blogs(title, slug),
        count
      `)
      .not('blog_id', 'is', null)
    
    if (error) throw error
    return data
  }
}
