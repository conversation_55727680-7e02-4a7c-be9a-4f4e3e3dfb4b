import { useEffect } from 'react'
import { useLocation } from 'react-router-dom'
import Header from './Header'
import Footer from './Footer'
import { analyticsService } from '../lib/supabase'

const Layout = ({ children }) => {
  const location = useLocation()

  // Track page views for analytics
  useEffect(() => {
    analyticsService.trackPageView(location.pathname)
  }, [location.pathname])

  return (
    <div className="min-h-screen bg-white dark:bg-gray-900 flex flex-col">
      <Header />
      <main className="flex-1">
        {children}
      </main>
      <Footer />
    </div>
  )
}

export default Layout
