import { Code, Users, Target, Award } from 'lucide-react'

const About = () => {
  const features = [
    {
      icon: Code,
      title: 'Technical Excellence',
      description: 'In-depth tutorials and guides covering the latest technologies and best practices in software development.'
    },
    {
      icon: Users,
      title: 'Community Driven',
      description: 'Built by developers, for developers. We focus on practical knowledge that you can apply immediately.'
    },
    {
      icon: Target,
      title: 'Focused Content',
      description: 'Carefully curated articles that cut through the noise and deliver actionable insights.'
    },
    {
      icon: Award,
      title: 'Quality First',
      description: 'Every article is thoroughly researched and tested to ensure accuracy and relevance.'
    }
  ]

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-blue-600 via-purple-600 to-indigo-700 text-white py-20">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h1 className="text-4xl md:text-5xl font-bold mb-6">
            About MyTechRead
          </h1>
          <p className="text-xl text-blue-100 leading-relaxed">
            Your trusted source for cutting-edge technology insights, programming tutorials, 
            and digital innovation stories.
          </p>
        </div>
      </section>

      {/* Main Content */}
      <section className="py-16">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Mission Statement */}
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-8 mb-12 border border-gray-200 dark:border-gray-700">
            <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-6">
              Our Mission
            </h2>
            <p className="text-lg text-gray-600 dark:text-gray-300 leading-relaxed mb-6">
              At MyTechRead, we believe that technology should be accessible to everyone. Our mission is to 
              bridge the gap between complex technical concepts and practical implementation, making cutting-edge 
              technology understandable and actionable for developers at all levels.
            </p>
            <p className="text-lg text-gray-600 dark:text-gray-300 leading-relaxed">
              We're passionate about sharing knowledge, fostering innovation, and building a community where 
              developers can learn, grow, and stay ahead of the rapidly evolving tech landscape.
            </p>
          </div>

          {/* Features Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-12">
            {features.map((feature, index) => {
              const Icon = feature.icon
              return (
                <div
                  key={index}
                  className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6 border border-gray-200 dark:border-gray-700 hover:shadow-md transition-shadow duration-300"
                >
                  <div className="flex items-center space-x-4 mb-4">
                    <div className="w-12 h-12 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center">
                      <Icon className="w-6 h-6 text-blue-600 dark:text-blue-400" />
                    </div>
                    <h3 className="text-xl font-semibold text-gray-900 dark:text-white">
                      {feature.title}
                    </h3>
                  </div>
                  <p className="text-gray-600 dark:text-gray-300 leading-relaxed">
                    {feature.description}
                  </p>
                </div>
              )
            })}
          </div>

          {/* What We Cover */}
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-8 border border-gray-200 dark:border-gray-700">
            <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-6">
              What We Cover
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                  Web Development
                </h3>
                <ul className="space-y-2 text-gray-600 dark:text-gray-300">
                  <li>• React, Vue, Angular frameworks</li>
                  <li>• Node.js and backend development</li>
                  <li>• Modern CSS and responsive design</li>
                  <li>• Performance optimization</li>
                </ul>
              </div>
              <div>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                  Emerging Technologies
                </h3>
                <ul className="space-y-2 text-gray-600 dark:text-gray-300">
                  <li>• Artificial Intelligence & Machine Learning</li>
                  <li>• Cloud computing and DevOps</li>
                  <li>• Mobile app development</li>
                  <li>• Blockchain and Web3</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}

export default About
