{"name": "bloging-website-automation", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@clerk/clerk-react": "^5.45.0", "@supabase/supabase-js": "^2.56.1", "date-fns": "^4.1.0", "lucide-react": "^0.542.0", "quill": "^2.0.3", "react": "^19.1.1", "react-dom": "^19.1.1", "react-is": "^19.1.1", "react-quill": "^2.0.0", "react-router-dom": "^7.8.2", "recharts": "^3.1.2"}, "devDependencies": {"@eslint/js": "^9.33.0", "@types/react": "^19.1.10", "@types/react-dom": "^19.1.7", "@vitejs/plugin-react": "^5.0.0", "autoprefixer": "^10.4.21", "eslint": "^9.33.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "postcss": "^8.5.6", "tailwindcss": "^3.4.17", "vite": "^7.1.2"}}